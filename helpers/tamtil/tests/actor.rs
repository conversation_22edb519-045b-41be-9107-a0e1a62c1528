//! # TAMTIL Comprehensive Blackbox Tests
//!
//! ## Complete Integration Tests - No Mocks, Stubs, or Fake Implementations
//!
//! These tests validate the entire TAMTIL system as a black box, testing only
//! through public APIs without accessing private fields or using any mock/stub
//! implementations. Every test exercises real functionality.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// TEST IMPLEMENTATIONS - REAL BUSINESS LOGIC
// ============================================================================

/// Calculator Action - Real Implementation
#[derive(Debug, <PERSON>lone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CalculatorAction {
    pub operation: String,
    pub operand: f64,
}

#[async_trait::async_trait]
impl Action for CalculatorAction {
    type Reaction = CalculatorReaction;

    async fn act(&self, memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        // Get current value
        let current_bytes = memories.recall("value").await?;
        let current_value = if let Some(bytes) = current_bytes {
            rkyv::from_bytes::<f64, rkyv::rancor::Error>(&bytes)
                .map_err(|e| TamtilError::Deserialization {
                    context: format!("Failed to deserialize current value: {}", e)
                })?
        } else {
            0.0
        };
        
        // Perform operation
        let new_value = match self.operation.as_str() {
            "add" => current_value + self.operand,
            "subtract" => current_value - self.operand,
            "multiply" => current_value * self.operand,
            "divide" => {
                if self.operand == 0.0 {
                    return Err(TamtilError::Validation {
                        message: "Division by zero".to_string()
                    });
                }
                current_value / self.operand
            }
            _ => return Err(TamtilError::Validation {
                message: format!("Unknown operation: {}", self.operation)
            }),
        };
        
        Ok(CalculatorReaction {
            old_value: current_value,
            new_value,
            operation: self.operation.clone(),
            operand: self.operand,
        })
    }
    
    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize calculator action: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize calculator action: {}", e)
            })
    }
}

/// Calculator Reaction - Real Implementation
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CalculatorReaction {
    pub old_value: f64,
    pub new_value: f64,
    pub operation: String,
    pub operand: f64,
}

impl Reaction for CalculatorReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: "value".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.new_value).unwrap().to_vec(),
            },
            MemoryOperation::Increment {
                key: "operation_count".to_string(),
                amount: 1,
            },
            MemoryOperation::Append {
                key: "history".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&format!("{} {} {} = {}",
                    self.old_value, self.operation, self.operand, self.new_value)).unwrap().to_vec(),
            },
        ]
    }
    
    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize calculator reaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize calculator reaction: {}", e)
            })
    }
}

// ============================================================================
// COMPREHENSIVE BLACKBOX TESTS
// ============================================================================

/// Test 1: Basic Actor System Functionality (Local Mode)
#[tokio::test]
async fn test_basic_actor_system() {
    // Create local platform for maximum test performance
    let platform = Platform::local("test_platform");
    
    // Create context
    platform.create_context("calculator_context").await.unwrap();
    let context_id = ActorId::new("calculator_context");
    
    // Create calculator actor
    let calculator_actor = GenericActor::<CalculatorAction>::new(ActorId::new("calculator"));
    platform.add_actor_to_context(&context_id, calculator_actor).await.unwrap();
    
    // Test addition
    let add_action = CalculatorAction {
        operation: "add".to_string(),
        operand: 5.0,
    };
    let action_bytes = add_action.to_bytes().unwrap();
    
    let reaction_bytes = platform.send_action(
        &context_id,
        &ActorId::new("calculator"),
        action_bytes
    ).await.unwrap();
    
    // Verify reaction
    let reaction = CalculatorReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.old_value, 0.0);
    assert_eq!(reaction.new_value, 5.0);
    assert_eq!(reaction.operation, "add");
    assert_eq!(reaction.operand, 5.0);
}

/// Test 2: Memory System and Event Sourcing
#[tokio::test]
async fn test_memory_system() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Test basic set/recall
    let operations = vec![
        MemoryOperation::Set {
            key: "test_key".to_string(),
            value: b"test_value".to_vec(),
        }
    ];
    memories.remember(operations).await.unwrap();
    
    let value = memories.recall("test_key").await.unwrap();
    assert_eq!(value, Some(b"test_value".to_vec()));
    
    // Test counters
    let operations = vec![
        MemoryOperation::Increment {
            key: "counter".to_string(),
            amount: 5,
        },
        MemoryOperation::Increment {
            key: "counter".to_string(),
            amount: 3,
        },
    ];
    memories.remember(operations).await.unwrap();
    
    let counter_value = memories.get_counter("counter").await.unwrap();
    assert_eq!(counter_value, 8);
}

/// Test 3: Consensus Protocol Functionality
#[tokio::test]
async fn test_consensus_protocol() {
    // Create 3-node consensus cluster
    let mut node1 = TamtilConsensus::new(1, vec![2, 3]);
    let mut node2 = TamtilConsensus::new(2, vec![1, 3]);
    let mut node3 = TamtilConsensus::new(3, vec![1, 2]);
    
    // Node 1 tries to become leader
    let reaction_bytes = b"test_reaction".to_vec();
    let actor_id = ActorId::new("test_actor");
    
    node1.propose_entry(reaction_bytes.clone(), actor_id.clone()).unwrap();
    
    // Get outgoing messages from node 1
    let messages = node1.take_outgoing_messages();
    assert_eq!(messages.len(), 2); // Should send prepare to 2 peers
    
    // Simulate message delivery to peers
    for (to_node, message) in messages {
        match to_node {
            2 => node2.handle_message(1, message).unwrap(),
            3 => node3.handle_message(1, message).unwrap(),
            _ => panic!("Unexpected target node"),
        }
    }
    
    // Get promise responses
    let node2_messages = node2.take_outgoing_messages();
    let node3_messages = node3.take_outgoing_messages();
    
    // Send promises back to node 1
    for (_, message) in node2_messages {
        node1.handle_message(2, message).unwrap();
    }
    for (_, message) in node3_messages {
        node1.handle_message(3, message).unwrap();
    }
    
    // Node 1 should now be leader and send accept messages
    let accept_messages = node1.take_outgoing_messages();
    assert!(!accept_messages.is_empty());
    
    // Verify node 1 is in leader state
    let (role, phase) = node1.get_state();
    assert_eq!(role, Role::Leader);
    assert_eq!(phase, Phase::Accept);
}

/// Test 4: Error Handling and Validation (Local Mode)
#[tokio::test]
async fn test_error_handling() {
    let platform = Platform::local("test_platform");
    platform.create_context("test_context").await.unwrap();
    let context_id = ActorId::new("test_context");
    
    let calculator_actor = GenericActor::<CalculatorAction>::new(ActorId::new("calculator"));
    platform.add_actor_to_context(&context_id, calculator_actor).await.unwrap();
    
    // Test division by zero
    let divide_by_zero = CalculatorAction {
        operation: "divide".to_string(),
        operand: 0.0,
    };
    let action_bytes = divide_by_zero.to_bytes().unwrap();
    
    let result = platform.send_action(
        &context_id,
        &ActorId::new("calculator"),
        action_bytes
    ).await;
    
    assert!(result.is_err());
    match result.unwrap_err() {
        TamtilError::Validation { message } => {
            assert_eq!(message, "Division by zero");
        }
        _ => panic!("Expected validation error"),
    }
}

/// Test 5: Storage Operations
#[tokio::test]
async fn test_storage_operations() {
    let mut storage = TamtilStorage::default();
    
    // Test atomic operations
    let entry = TamtilEntry {
        reaction_bytes: b"test_reaction".to_vec(),
        actor_id: ActorId::new("test_actor"),
        sequence: 1,
        timestamp: 12345,
        proposer: 1,
    };
    
    let ops = vec![
        StorageOp::AppendEntry(entry.clone()),
        StorageOp::SetDecidedIndex(1),
    ];
    
    storage.write_atomically(ops).unwrap();
    
    assert_eq!(storage.get_log_len(), 1);
    assert_eq!(storage.get_decided_idx(), 1);
    
    let entries = storage.get_entries(0, 1).unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].sequence, 1);
}

/// Test 6: Unified CLI/Web Development Experience
#[tokio::test]
async fn test_unified_development_experience() {
    // Test that same actor code works in both modes

    // Local mode (CLI)
    let local_platform = Platform::local("cli_calculator");
    assert!(local_platform.is_local());
    assert!(!local_platform.is_distributed());
    assert_eq!(local_platform.mode(), DeploymentMode::Local);

    // Distributed mode (Web)
    let distributed_platform = Platform::distributed("web_calculator", 1, vec![2, 3]);
    assert!(!distributed_platform.is_local());
    assert!(distributed_platform.is_distributed());
    assert_eq!(distributed_platform.mode(), DeploymentMode::Distributed);

    // Same actor setup for both platforms
    for (name, platform) in [("local", &local_platform), ("distributed", &distributed_platform)] {
        platform.create_context("calculator_context").await.unwrap();
        let context_id = ActorId::new("calculator_context");

        let calculator_actor = GenericActor::<CalculatorAction>::new(ActorId::new("calculator"));
        platform.add_actor_to_context(&context_id, calculator_actor).await.unwrap();

        // Same action works in both modes
        let add_action = CalculatorAction {
            operation: "add".to_string(),
            operand: 42.0,
        };

        let reaction_bytes = platform.send_action(
            &context_id,
            &ActorId::new("calculator"),
            add_action.to_bytes().unwrap()
        ).await.unwrap();

        let reaction = CalculatorReaction::from_bytes(&reaction_bytes).unwrap();
        assert_eq!(reaction.new_value, 42.0);

        println!("✅ {} mode: Same actor code produced same result", name);
    }
}
